"""
generate_ideas_csv_no_repeats.py (Enhanced Anti-Repetition Version with Retry Logic)
──────────────────────────────────────────────────────────────────
Create a 2,000-row CSV with unique single-object food stock photo concepts
that actively prevents repetition through multiple strategies and includes robust retry logic.
"""

import csv
import threading
import time
import textwrap
import random
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import google.generativeai as genai
import tqdm
from collections import defaultdict
import re
import json

# Configure logging to be less verbose
logging.basicConfig(level=logging.ERROR)

# ---------------------------------------------------------------------
# 1. Configuration
# ---------------------------------------------------------------------
# TODO: Replace with your actual Gemini API key
GOOGLE_API_KEY = "AIzaSyAPOtiX70Sm_sHKyJ6Rrgl2tbJ_NswfR1A"  # Replace with your actual key
genai.configure(api_key=GOOGLE_API_KEY)
model = genai.GenerativeModel(model_name="gemini-2.0-flash")

OUTPUT_FILE = "ideas.csv"           # Output filename
N_ROWS = 2_000                      # Exactly 2,000 ideas
MAX_WORKERS = 15                    # Reduced for stability
BATCH_SIZE = 25                     # Much smaller batches for higher success

# Retry configuration
MAX_RETRIES = 5                     # Maximum retries per batch
RETRY_DELAY_BASE = 2                # Base delay between retries (seconds)
RETRY_BACKOFF = 2                   # Exponential backoff multiplier
RATE_LIMIT_DELAY = 60              # Delay when hitting rate limits

# Thread locks and tracking
csv_lock = threading.Lock()
tracking_lock = threading.Lock()
processed_count = 0
failed_count = 0
retry_count = 0

# Anti-repetition tracking
used_objects = set()
used_keywords = defaultdict(int)
category_counts = defaultdict(int)

# ---------------------------------------------------------------------
# 2. Comprehensive food object database
# ---------------------------------------------------------------------
FOOD_CATEGORIES = {
    "citrus_fruits": [
        "lemon", "lime", "orange", "grapefruit", "mandarin", "tangerine", 
        "blood orange", "meyer lemon", "key lime", "bergamot", "yuzu", "kumquat"
    ],
    "berries": [
        "strawberry", "blueberry", "raspberry", "blackberry", "cranberry", 
        "gooseberry", "elderberry", "boysenberry", "loganberry", "huckleberry",
        "cloudberry", "acai berry", "goji berry", "mulberry"
    ],
    "tropical_fruits": [
        "pineapple", "mango", "papaya", "passion fruit", "dragon fruit", 
        "star fruit", "kiwi", "coconut", "guava", "lychee", "rambutan",
        "durian", "jackfruit", "plantain", "breadfruit"
    ],
    "stone_fruits": [
        "peach", "plum", "apricot", "nectarine", "cherry", "date",
        "fig", "persimmon", "quince"
    ],
    "root_vegetables": [
        "carrot", "potato", "sweet potato", "turnip", "radish", "beet",
        "parsnip", "rutabaga", "daikon", "jicama", "yam", "cassava"
    ],
    "leafy_greens": [
        "spinach leaf", "kale leaf", "lettuce leaf", "arugula leaf", 
        "chard leaf", "collard green leaf", "cabbage leaf", "bok choy leaf",
        "watercress sprig", "mustard green leaf"
    ],
    "herbs": [
        "basil leaf", "cilantro sprig", "parsley sprig", "dill sprig",
        "thyme sprig", "oregano sprig", "sage leaf", "rosemary sprig",
        "mint leaf", "tarragon sprig", "chives", "bay leaf"
    ],
    "spices": [
        "cinnamon stick", "vanilla bean", "cardamom pod", "star anise",
        "clove", "nutmeg", "allspice berry", "peppercorn", "juniper berry",
        "coriander seed", "fennel seed", "cumin seed"
    ],
    "mushrooms": [
        "button mushroom", "shiitake mushroom", "portobello mushroom",
        "oyster mushroom", "enoki mushroom", "chanterelle", "morel",
        "porcini mushroom", "cremini mushroom", "maitake mushroom"
    ],
    "seafood": [
        "salmon fillet", "tuna steak", "cod fillet", "shrimp", "scallop",
        "lobster tail", "crab claw", "oyster", "mussel", "sardine",
        "mackerel", "sea bass fillet", "halibut steak"
    ],
    "nuts_seeds": [
        "walnut", "almond", "pecan", "hazelnut", "pistachio", "cashew",
        "brazil nut", "macadamia nut", "pine nut", "sunflower seed",
        "pumpkin seed", "chia seed", "flax seed", "sesame seed"
    ],
    "cheeses": [
        "cheddar wedge", "brie wheel", "goat cheese round", "blue cheese wedge",
        "parmesan chunk", "mozzarella ball", "feta block", "gouda slice",
        "camembert wheel", "roquefort wedge"
    ],
    "chocolates": [
        "dark chocolate square", "milk chocolate bar", "white chocolate piece",
        "truffle", "cocoa bean", "chocolate chip", "chocolate shaving",
        "chocolate coin", "chocolate wafer"
    ]
}

# ---------------------------------------------------------------------
# 3. Anti-repetition functions
# ---------------------------------------------------------------------
def extract_main_object(description):
    """Extract the main food object from a description - more flexible for variations."""
    # Remove common modifiers and get core object
    desc_lower = description.lower()
    
    # List of main food objects to look for
    all_objects = []
    for category in FOOD_CATEGORIES.values():
        all_objects.extend(category)
    
    # Find the base object but allow variations
    for obj in all_objects:
        if obj in desc_lower:
            return obj
    
    # More flexible fallback - extract meaningful food-related words
    words = desc_lower.split()
    food_words = []
    skip_words = {'with', 'isolated', 'white', 'background', 'and', 'the', 'on', 'in', 'of', 'fresh', 'organic', 'single'}
    
    for word in words[:4]:  # Look at first 4 words
        if len(word) > 3 and word not in skip_words:
            food_words.append(word)
            if len(food_words) >= 2:  # Take up to 2 meaningful words
                break
    
    return ' '.join(food_words) if food_words else f'food_item_{random.randint(1000, 9999)}'

def get_unused_categories():
    """Get categories that haven't been used much."""
    with tracking_lock:
        sorted_categories = sorted(FOOD_CATEGORIES.keys(), 
                                 key=lambda x: category_counts[x])
        return sorted_categories[:len(FOOD_CATEGORIES)//2]  # Return less-used half

def build_diverse_batch_prompt(batch_size: int, start_id: int, batch_number: int) -> str:
    """
    Craft a prompt that encourages creativity and variations without being too restrictive.
    """
    end_id = start_id + batch_size - 1
    
    # Get some focus categories but don't be too restrictive
    focus_categories = get_unused_categories()
    
    prompt = f"""You are a professional stock photography concept generator creating BATCH #{batch_number}.

MISSION: Generate {batch_size} creative single food object concepts with interesting variations and presentations.

FOCUS AREAS: {', '.join(focus_categories[:8])} - but feel free to explore ANY food category creatively.

CORE REQUIREMENTS:
- Each concept must describe ONE SINGLE OBJECT (not groups or collections)
- Create interesting variations through different presentations, angles, styles, effects
- Each description must end with "isolated on white background"
- Be creative with presentations: cuts, lighting, effects, states, textures

OUTPUT FORMAT: Return ONLY a valid JSON array with NO additional text:
[
  {{"id": "{start_id:04d}", "description": "single object description isolated on white background"}},
  {{"id": "{start_id+1:04d}", "description": "single object description isolated on white background"}},
  {{"id": "{end_id:04d}", "description": "single object description isolated on white background"}}
]

CREATIVE VARIATION STRATEGIES:
🎨 PRESENTATION STYLES: fresh, dried, frozen, grilled, roasted, caramelized, candied, pickled
🔥 VISUAL EFFECTS: steam, smoke, water droplets, ice crystals, oil sheen, frost, glow
✂️ CUTS & SHAPES: sliced, diced, spiral cut, ribbon cut, julienned, cross-section, halved, quartered
💡 LIGHTING EFFECTS: backlighting, dramatic shadows, golden light, soft glow, rim lighting
🌊 MOTION & ACTION: splashing, dripping, melting, cracking, bubbling, sizzling
🔍 DETAIL FOCUS: macro photography, texture close-up, surface patterns, natural imperfections

FOOD CATEGORIES TO EXPLORE CREATIVELY:
• Fruits: citrus, berries, tropical, stone fruits, melons - in various states and cuts
• Vegetables: roots, leafy greens, pods, squashes - with different preparations
• Proteins: meats, seafood, eggs - various cuts and cooking states  
• Dairy: cheeses, butter, cream - different textures and forms
• Grains & Legumes: rice, quinoa, beans, lentils - various preparations
• Nuts & Seeds: whole, cracked, sprouted, roasted variations
• Herbs & Spices: fresh, dried, ground, whole - artistic presentations
• Baked Goods: breads, pastries, crackers - different textures and cuts
• Chocolates & Sweets: various forms, melted, solid, powdered

EXAMPLE VARIATIONS (for inspiration):
✅ "golden honey drizzling from wooden dipper isolated on white background"
✅ "cross-section of ripe kiwi with geometric seed pattern isolated on white background"
✅ "steaming cup of coffee with artistic foam pattern isolated on white background"
✅ "crystallized ginger piece with sugar coating isolated on white background"
✅ "purple cabbage leaf with water droplets isolated on white background"

CREATIVE FREEDOM:
- Feel free to combine foods with natural elements (honey, salt crystals, ice)
- Explore different ripeness stages, cooking levels, preparation methods
- Use artistic and commercial photography terminology
- Think about what would make compelling stock photos
- Variations of similar foods are PERFECTLY ACCEPTABLE if presented differently

Generate {batch_size} creative single object concepts as a JSON array:"""

    return prompt

# ---------------------------------------------------------------------
# 4. Enhanced retry mechanism
# ---------------------------------------------------------------------
def is_rate_limit_error(error_text):
    """Check if the error is related to rate limiting."""
    rate_limit_indicators = [
        "quota", "rate limit", "too many requests", "429", 
        "resource_exhausted", "rate exceeded", "throttled"
    ]
    error_lower = str(error_text).lower()
    return any(indicator in error_lower for indicator in rate_limit_indicators)

def handle_api_response(response_text):
    """Clean and validate API response."""
    if not response_text:
        return None, "Empty response"
    
    # Clean up response text
    response_text = response_text.strip()
    
    # Remove markdown code blocks if present
    if response_text.startswith("```"):
        lines = response_text.split('\n')
        # Find the actual JSON content
        json_start = -1
        json_end = -1
        for i, line in enumerate(lines):
            if line.strip().startswith('['):
                json_start = i
                break
        for i in range(len(lines)-1, -1, -1):
            if lines[i].strip().endswith(']'):
                json_end = i
                break
        
        if json_start != -1 and json_end != -1:
            response_text = '\n'.join(lines[json_start:json_end+1])
    
    # Find JSON array in the response
    start_idx = response_text.find('[')
    end_idx = response_text.rfind(']')
    
    if start_idx == -1 or end_idx == -1:
        return None, "No JSON array found"
    
    json_text = response_text[start_idx:end_idx+1]
    
    try:
        data = json.loads(json_text)
        if not isinstance(data, list):
            return None, "Response is not a list"
        return data, None
    except json.JSONDecodeError as e:
        return None, f"JSON decode error: {e}"

def generate_batch_with_retry(batch_info):
    """
    Generate a batch with comprehensive retry logic.
    """
    global retry_count
    
    start_id, batch_size, batch_number = batch_info
    
    for attempt in range(MAX_RETRIES + 1):
        try:
            # Calculate delay with exponential backoff
            if attempt > 0:
                delay = RETRY_DELAY_BASE * (RETRY_BACKOFF ** (attempt - 1))
                delay += random.uniform(0, 1)  # Add jitter
                print(f"⏳ Retrying batch #{batch_number} (attempt {attempt + 1}/{MAX_RETRIES + 1}) after {delay:.1f}s...")
                time.sleep(delay)
                
                with tracking_lock:
                    retry_count += 1
            
            # Add base delay to prevent rate limiting
            time.sleep(random.uniform(0.3, 0.7))
            
            prompt = build_diverse_batch_prompt(batch_size, start_id, batch_number)
            
            # Make API call with timeout
            response = model.generate_content(prompt)
            
            if not response or not response.text:
                raise Exception("Empty response from API")
            
            # Handle and parse response
            ideas_data, error = handle_api_response(response.text)
            
            if error:
                raise Exception(f"Response parsing error: {error}")
            
            # Process the ideas - much more flexible approach
            results = []
            batch_objects = set()
            processed_descriptions = set()
            
            for idea in ideas_data:
                if not isinstance(idea, dict) or 'id' not in idea or 'description' not in idea:
                    continue
                
                desc = idea['description'].strip()
                
                # Ensure proper ending
                if not desc.endswith('isolated on white background'):
                    if 'isolated' not in desc.lower():
                        desc += ' isolated on white background'
                
                # Skip exact duplicates within this batch
                if desc.lower() in processed_descriptions:
                    continue
                processed_descriptions.add(desc.lower())
                
                # Basic collection word check (still important)
                desc_lower = desc.lower()
                collection_words = ['group', 'pile', 'arrangement', 'several', 
                                  'collection', 'scattered', 'assorted', 'variety', 
                                  'bunch', 'cluster', 'multiple', 'many', 'stack of']
                
                if any(word in desc_lower for word in collection_words):
                    continue
                
                # Extract main object for loose tracking (but don't be too strict)
                main_obj = extract_main_object(desc)
                
                # Allow variations of the same object - just avoid too many identical ones
                variation_key = f"{main_obj}_{hash(desc.lower()) % 1000}"
                
                # Very loose repetition check - allow many variations
                with tracking_lock:
                    similar_count = sum(1 for obj in used_objects if main_obj.split()[0] in obj)
                    if similar_count > 20:  # Only avoid if we have 20+ very similar items
                        continue
                
                results.append({
                    'id': idea['id'],
                    'description': desc,
                    'main_object': variation_key  # Use variation key instead of base object
                })
            
            # Much lower threshold - accept if we got any reasonable results
            min_required = max(3, int(batch_size * 0.3))  # At least 3 items or 30% of requested
            
            if len(results) >= min_required:
                # Update global tracking with variations
                with tracking_lock:
                    for result in results:
                        used_objects.add(result['main_object'])
                        # Update category counts more loosely
                        base_object = result['main_object'].split('_')[0]
                        for category, objects in FOOD_CATEGORIES.items():
                            if any(obj in result['description'].lower() for obj in objects):
                                category_counts[category] += 1
                                break
                        else:
                            # If no category match, create a generic one
                            category_counts['variations'] += 1
                
                return results
            else:
                raise Exception(f"Insufficient results: got {len(results)}, needed {min_required}")
        
        except Exception as e:
            error_msg = str(e)
            
            # Handle rate limiting
            if is_rate_limit_error(error_msg):
                print(f"⚠️  Rate limit detected for batch #{batch_number}. Waiting {RATE_LIMIT_DELAY}s...")
                time.sleep(RATE_LIMIT_DELAY)
                continue
            
            # Log the attempt
            if attempt < MAX_RETRIES:
                print(f"❌ Batch #{batch_number} attempt {attempt + 1} failed: {error_msg}")
            else:
                print(f"💀 Batch #{batch_number} FINAL FAILURE after {MAX_RETRIES + 1} attempts: {error_msg}")
    
    return []

# ---------------------------------------------------------------------
# 5. Enhanced batch processing
# ---------------------------------------------------------------------
def process_diverse_batch(batch_info):
    """
    Process a batch with diversity tracking and retry logic.
    """
    start_id, batch_size, batch_number = batch_info
    results = generate_batch_with_retry(batch_info)
    
    if results:
        csv_rows = []
        for result in results:
            csv_rows.append([result['id'], result['description']])
        
        return {
            'batch_info': batch_info,
            'csv_rows': csv_rows,
            'count': len(csv_rows),
            'objects': [r['main_object'] for r in results]
        }
    else:
        return None

# ---------------------------------------------------------------------
# 6. Enhanced CSV writing with tracking
# ---------------------------------------------------------------------
def write_to_csv_with_tracking(output_file, result_data):
    """
    Thread-safe CSV writing with object tracking.
    """
    global processed_count, failed_count
    
    with csv_lock:
        try:
            with open(output_file, 'a', newline='', encoding='utf-8') as csv_file:
                csv_writer = csv.writer(csv_file, quoting=csv.QUOTE_ALL)
                for row in result_data['csv_rows']:
                    csv_writer.writerow(row)
            
            processed_count += result_data['count']
            batch_number = result_data['batch_info'][2]
            objects_created = ', '.join(result_data['objects'][:3])
            if len(result_data['objects']) > 3:
                objects_created += f" +{len(result_data['objects'])-3} more"
            
            print(f"✅ Batch #{batch_number}: {result_data['count']} unique objects ({objects_created})")
            
        except Exception as e:
            print(f"❌ Error writing batch #{result_data['batch_info'][2]}: {e}")
            failed_count += result_data['count']

# ---------------------------------------------------------------------
# 7. Enhanced main execution
# ---------------------------------------------------------------------
def main():
    """
    Main function with enhanced diversity control and retry logic.
    """
    global processed_count, failed_count, retry_count
    
    print(f"🚀 Starting generation of {N_ROWS} creative single object ideas...")
    print(f"📁 Output file: {OUTPUT_FILE}")
    print(f"🎨 Flexible variation system: ACTIVE (allows creative variations)")
    print(f"🔄 Retry system: ACTIVE (max {MAX_RETRIES} retries per batch)")
    print(f"⚡ Rate limiting protection: ENABLED")
    
    # Create CSV file with headers
    with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csv_file:
        csv_writer = csv.writer(csv_file, quoting=csv.QUOTE_ALL)
        csv_writer.writerow(['idea_id', 'idea_description'])
    
    print(f"Created {OUTPUT_FILE} with headers")
    
    # Create batches with batch numbers
    batches = []
    batch_number = 1
    for i in range(1, N_ROWS + 1, BATCH_SIZE):
        remaining = min(BATCH_SIZE, N_ROWS - i + 1)
        batches.append((i, remaining, batch_number))
        batch_number += 1
    
    print(f"📦 Created {len(batches)} diverse batches of ~{BATCH_SIZE} unique objects each")
    
    start_time = time.time()
    
    # Process batches with retry logic
    failed_batches = []
    
    for batch_info in tqdm.tqdm(batches, desc='Generating unique objects', ncols=100):
        try:
            result = process_diverse_batch(batch_info)
            if result:
                write_to_csv_with_tracking(OUTPUT_FILE, result)
            else:
                failed_count += batch_info[1]
                failed_batches.append(batch_info)
                print(f"💀 FINAL FAILURE: Batch #{batch_info[2]}")
                
        except Exception as e:
            failed_count += batch_info[1]
            failed_batches.append(batch_info)
            print(f"💥 Exception in batch #{batch_info[2]}: {e}")
    
    elapsed = time.time() - start_time
    
    # Final statistics with diversity metrics
    with tracking_lock:
        unique_objects = len(used_objects)
        most_used_categories = sorted(category_counts.items(), 
                                    key=lambda x: x[1], reverse=True)[:5]
    
    print(f"\n" + "="*70)
    print(f"📊 UNIQUE OBJECT GENERATION COMPLETE")
    print(f"⏱️  Total time: {elapsed:.1f} seconds")
    print(f"✅ Successfully processed: {processed_count} unique single objects")
    print(f"🎯 Unique objects created: {unique_objects}")
    print(f"🔄 Total retries attempted: {retry_count}")
    print(f"💀 Failed batches: {len(failed_batches)}")
    print(f"❌ Failed ideas: {failed_count}")
    print(f"📈 Success rate: {processed_count/(processed_count+failed_count)*100:.1f}%")
    print(f"🚀 Average speed: {processed_count/elapsed:.1f} ideas/second")
    
    print(f"\n📊 DIVERSITY STATISTICS:")
    print(f"🎯 Uniqueness ratio: {unique_objects}/{processed_count} ({unique_objects/processed_count*100:.1f}%)")
    print(f"📋 Top categories used:")
    for category, count in most_used_categories:
        print(f"   • {category.replace('_', ' ').title()}: {count} objects")
    
    if failed_batches:
        print(f"\n💀 FAILED BATCHES: {[b[2] for b in failed_batches]}")
        
        # Option to retry failed batches
        retry_failed = input(f"\n🔄 Retry {len(failed_batches)} failed batches? (y/n): ").lower().strip()
        if retry_failed == 'y':
            print(f"🔄 Retrying {len(failed_batches)} failed batches...")
            for batch_info in failed_batches:
                try:
                    result = process_diverse_batch(batch_info)
                    if result:
                        write_to_csv_with_tracking(OUTPUT_FILE, result)
                        failed_count -= batch_info[1]  # Remove from failed count
                        print(f"✅ Recovered batch #{batch_info[2]}")
                    else:
                        print(f"💀 Still failed: Batch #{batch_info[2]}")
                except Exception as e:
                    print(f"💥 Exception in retry batch #{batch_info[2]}: {e}")
    
    print(f"📁 Final file: {Path(OUTPUT_FILE).resolve()}")
    
    if processed_count >= N_ROWS * 0.95:
        print(f"🎉 SUCCESS! Generated {processed_count} unique single objects with minimal repetition.")
    else:
        print(f"⚠️  Generated {processed_count} ideas out of {N_ROWS} target. Consider running the retry option.")

if __name__ == "__main__":
    main()