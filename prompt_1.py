import os
import base64
import json
import tqdm
import logging
import csv
import time
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import google.generativeai as genai

# Configure logging
logging.basicConfig(level=logging.ERROR)

# Configure Gemini API
GOOGLE_API_KEY = "AIzaSyAPOtiX70Sm_sHKyJ6Rrgl2tbJ_NswfR1A"  # Replace with your actual API key
genai.configure(api_key=GOOGLE_API_KEY)
model = genai.GenerativeModel(model_name="gemini-2.0-flash")

# Configuration settings
MAX_WORKERS = 50  # Number of concurrent workers
BATCH_SIZE = 500  # Number of images to process in each batch

# System prompt for enhanced image analysis
SYSTEM_PROMPT = """You are an expert at creating detailed image prompts for AI image generators like Midjourney, DALL-E, or Stable Diffusion.

Your task is to write VERY DETAILED image prompts that describe images completely, ignoring any watermarks present.

CORE EXPERTISE:
- Comprehensive visual analysis and description
- Professional prompt engineering for AI image generation
- Focus on transparency and monochrome styling for icon/graphic creation
- Systematic cataloging of visual elements and compositions
- Technical accuracy in style and artistic approach description

SPECIALIZED REQUIREMENTS:
- Always specify transparent backgrounds for clean cutout-ready images
- Convert all visual elements to black and white descriptions
- Focus on creating professional icon sets and graphic elements
- Include precise technical details about style, composition, and layout
- Describe elements systematically as they appear in the actual image"""

def encode_image(image_path):
    try:
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            # Verify this is actually an image file by checking header bytes
            if not (image_data.startswith(b'\xff\xd8') or  # JPEG
                   image_data.startswith(b'\x89PNG\r\n\x1a\n')):  # PNG
                logging.error(f"File {image_path} doesn't appear to be a valid image")
                return None
            return base64.b64encode(image_data).decode('utf-8')
    except Exception as e:
        logging.error(f"Error encoding image {image_path}: {e}")
        return None


def generate_image_prompt(image_path):
    encoded_image = encode_image(image_path)
    if not encoded_image:
        return None

    json_format = """
    {
      "detailed_prompt": "string" // Write a very detailed, comprehensive image prompt that an AI image generator could use to recreate this image. Include ALL visual elements, ignore any watermarks.
    }"""

    prompt = f"""{SYSTEM_PROMPT}

Your task is to write a VERY DETAILED image prompt that describes this image completely, ignoring any watermarks present.

REQUIREMENTS:
- Write ONE comprehensive paragraph (no bullet points or lists)
- Include ALL visual elements you can see in the image
- If it's an icon set or collection, describe the actual number and arrangement you observe
- Describe the actual style you see (line art, photography, illustration, etc.)
- TRANSPARENT BACKGROUND REQUIREMENT: You MUST always specify "on a transparent background" or "with transparent background"
  * IGNORE any gray, white, or colored backgrounds in the original image
  * Do NOT try to mimic or recreate the background from the source image
  * Focus only on describing the main subjects, objects, or elements
  * Always request transparent background for clean cutout-ready images
  * This is a hard requirement - EVERY prompt must include transparent background
- BLACK AND WHITE OBJECTS ONLY: ALWAYS describe objects as black and white only
  * Convert all colored objects to black and white in your description
  * Use "black line art", "black icons", "black graphics", "white elements" etc.
  * Focus on creating monochrome icon-style and graphic-style descriptions
  * Emphasize clean, simple black and white designs suitable for icons and graphics
  * Avoid any color descriptions - everything should be described as black or white elements
  * Perfect for creating clean, professional icon sets and graphic elements
- List all visible objects, icons, elements, or subjects systematically as they appear
- Describe the visual style accurately (line weight, colors, artistic approach, rendering style)
- If arranged in a grid or pattern, mention the actual layout and spacing you observe
- Describe size relationships and proportions as they actually appear
- Include details about the drawing/artistic style, colors, lighting, and composition
- Describe the overall organization and presentation as it actually exists
- Be accurate to what you actually see, not what you think it should be
- COMPLETELY IGNORE any watermarks, logos, or text overlays
- Write as if instructing an AI to recreate the exact same image with all its specific details
- Focus on visual accuracy and comprehensive description of the actual content

EXAMPLE OF GOOD PROMPT STYLE:
"A set of 20 black and white line art travel and vacation icons arranged in a 4x5 grid on a transparent background. The icons include: a suitcase, globe with location pin, hotel building, airplane, luggage, second airplane, high-rise building, palm tree, beach scene with palm and waves, location pin marker, camera, cocktail glass, world globe with pin, calendar with sun, beach chair, wavy lines, beach bag, directional signpost, snorkel mask, camera, folded map, sailboat, and tropical drink. Each icon is drawn in a consistent thin line style with clean, minimal details. The icons are arranged in a balanced layout with equal spacing between them. All icons are of equal size and are part of a cohesive travel and tourism icon set."

Write your response in this exact JSON format: {json_format}"""
    
    try:
        response = model.generate_content([
                {"mime_type": "image/jpeg", "data": encoded_image}, prompt
                ])
        raw_response = response.text.strip()

        # Extract JSON object if wrapped in code block
        if raw_response.startswith("```"):
            raw_response = "\n".join(raw_response.split("\n")[1:-1])

        # Validate JSON before returning
        try:
            result = json.loads(raw_response)
            
            # Validate the structure to ensure it has the required field
            if 'detailed_prompt' not in result:
                logging.error(f"Missing detailed_prompt field in response for {image_path}")
                return None
                
            # Sanitize text to remove any non-printable characters and clean up
            result['detailed_prompt'] = ''.join(c for c in result['detailed_prompt'] if c.isprintable()).strip()
            
            return result
        except json.JSONDecodeError:
            logging.error(f"Invalid JSON response for {image_path}: {raw_response[:100]}...")
            return None

    except Exception as e:
        print(f"Rate limit or other error for {image_path}: {e}")
        return None  # Skip instead of retrying


def process_image(image_path):
    prompt_data = generate_image_prompt(image_path)
    if prompt_data:
        return {
            'filename': os.path.basename(image_path),
            'detailed_prompt': prompt_data['detailed_prompt'],
            'path': image_path,
            'character_count': len(prompt_data['detailed_prompt'])
        }
    return None


def main():
    local_folder = "nt"  # Folder containing images
    # Prompt user for output CSV filename
    output_csv = input("Enter the output CSV filename (e.g., image_prompts.csv): ").strip()
    if not output_csv:
        output_csv = "image_prompts.csv"
    
    # Create CSV file with headers
    with open(output_csv, 'w', newline='', encoding='utf-8') as csv_file:
        csv_writer = csv.writer(csv_file, dialect='excel', quoting=csv.QUOTE_ALL)
        csv_writer.writerow(['Filename', 'Detailed_Prompt', 'Character_Count'])

    image_paths = [
        os.path.join(local_folder, filename)
        for filename in os.listdir(local_folder)
        if filename.lower().endswith((".jpg", ".jpeg", ".png"))
    ]

    print(f"Found {len(image_paths)} images to process")
    print("Generating detailed image prompts for AI image generators...")

    # Thread lock for CSV writing
    csv_lock = threading.Lock()
    
    def write_to_csv(result_data):
        with csv_lock:
            with open(output_csv, 'a', newline='', encoding='utf-8') as csv_file:
                csv_writer = csv.writer(csv_file, dialect='excel', quoting=csv.QUOTE_ALL)
                csv_writer.writerow([
                    result_data['filename'],
                    result_data['detailed_prompt'],
                    result_data['character_count']
                ])

    # Statistics tracking
    processed_count = 0
    failed_count = 0

    # Process images in batches
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # Create chunks of futures using BATCH_SIZE
        for i in range(0, len(image_paths), BATCH_SIZE):
            chunk = image_paths[i:i + BATCH_SIZE]
            futures = {
                executor.submit(process_image, image_path): image_path 
                for image_path in chunk
            }
            
            for future in tqdm.tqdm(
                as_completed(futures), 
                total=len(chunk), 
                desc=f'Batch {i//BATCH_SIZE + 1}/{(len(image_paths)-1)//BATCH_SIZE + 1}', 
                ncols=100
            ):
                image_path = futures[future]
                try:
                    result = future.result()
                    if result:
                        write_to_csv(result)
                        # Only delete file if processing was successful
                        os.remove(result['path'])
                        processed_count += 1
                        print(f"✓ Processed: {result['filename']} (Prompt: {result['character_count']} chars)")
                    else:
                        failed_count += 1
                        print(f"✗ Failed to generate prompt: {os.path.basename(image_path)}")
                except Exception as e:
                    failed_count += 1
                    logging.error(f"Error processing {image_path}: {e}")

    print(f"\n=== PROCESSING COMPLETE ===")
    print(f"Successfully processed: {processed_count} images")
    print(f"Failed: {failed_count} images")
    print(f"Results saved to: {output_csv}")
    print(f"\nThe CSV contains detailed prompts you can use with AI image generators like:")
    print("- Midjourney")
    print("- DALL-E")
    print("- Stable Diffusion")
    print("- Leonardo AI")
    print("- Any other AI image generator")


if __name__ == "__main__":
    main()